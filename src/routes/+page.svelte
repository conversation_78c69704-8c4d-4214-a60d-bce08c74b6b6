<script lang="ts">
    import type {RoundChartOptions} from '../components/charts/round-charts/round-charts';
    import type {BarChartOptions} from '../components/charts/bar-charts/bar-charts';
    import KpRoundChart from '../components/charts/round-charts/KpRoundChart.svelte';
    import KpBarChart from '../components/charts/bar-charts/KpBarChart.svelte';

    const donutChartOptions: RoundChartOptions = {
        type: 'donut',
        startAngle: 0,
        chartSize: '250px',
        legendOptions: {
            placement: 'left',
            orientation: 'column',
            fromChartOffset: '30px'
        },
        strokeWidth: 3,
        dataset: [
            {
                color: '#e84848',
                label: 'Test 1',
                value: 1000
            },
            {
                color: '#40b0ac',
                label: 'Test 2',
                value: 500
            },
            {
                color: '#ce42ee',
                label: 'Test 3',
                value: 250
            }
        ]
    }

    const donutChartOptions2: RoundChartOptions = {
        type: 'donut',
        startAngle: 0,
        chartSize: '100px',
        strokeWidth: 10,
        legendOptions: {
            placement: 'bottom',
            orientation: 'row',
            fromChartOffset: '10px'
        },
        dataset: [
            {
                color: '#e84848',
                label: 'Test 1',
                value: 1000
            },
            {
                color: '#ce42ee',
                label: 'Test 2',
                value: 500
            },
            {
                color: '#426aee',
                label: 'Test 3',
                value: 250
            }
        ]
    }

    const ratioChartOptions: BarChartOptions = {
        type: 'ratio',
        orientation: 'horizontal',
        legendOptions: {
            placement: 'top',
            fromChartOffset: '10px'
        },
        width: '100%',
        maxValue: 5000,
        dataset: [
            {
                color: '#e78c30',
                label: 'Test 1',
                value: 1000
            },
            {
                color: '#8fcb4a',
                label: 'Test 2',
                value: 500
            },
            {
                color: '#3f3fd0',
                label: 'Test 3',
                value: 250
            }
        ]
    }

    const columnChartOptions: BarChartOptions = {
        type: 'columns',
        orientation: 'vertical',
        legendOptions: {
            placement: 'top'
        },
        width: '500px',
        dataset: [
            {
                color: '#e78c30',
                label: 'Test 1',
                value: 1000
            },
            {
                color: '#8fcb4a',
                label: 'Test 2',
                value: 500
            },
            {
                color: '#3f3fd0',
                label: 'Test 3',
                value: 250
            }
        ]
    }
</script>

<div class="charts-container">
    <div class="round-charts-container">
        <div class="chart-container">
            <h1>Donut round chart</h1>
            <KpRoundChart options="{donutChartOptions}"/>
        </div>

        <div class="chart-container">
            <h1>Donut round chart 2</h1>
            <KpRoundChart options="{donutChartOptions2}"/>
        </div>
    </div>

    <div class="chart-container">
        <h1>Ratio bar chart</h1>
        <KpBarChart options="{ratioChartOptions}"/>
    </div>

    <div class="chart-container">
        <h1>Columns bar chart</h1>
        <KpBarChart options="{columnChartOptions}"/>
    </div>

    <div class="chart-container">
        <h1>Segmented columns bar chart</h1>
        <KpBarChart options="{columnChartOptions}"/>
    </div>
</div>

<style>
    .charts-container {
        display: flex;
        flex-direction: column;
        gap: 32px;
        padding: 24px;

        .chart-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
            padding: 24px;
            border: 1px solid #DDD;

            h1 {
                margin: 0;
                font-size: 20px;
            }
        }

        .round-charts-container {
            display: flex;

            .chart-container {
                flex: 1;
            }
        }
    }
</style>