<script lang="ts">
    import type {LabeledRecord} from './types';
    import {createEventDispatcher} from 'svelte';
    import {MENTION_OTHER_PREFIX, MENTION_USER_PREFIX} from './constants';
    import DummyPopover from './dummy/DummyPopover.svelte';
    import {
        buildMentionRegex,
        createMentionSpan,
        extractRawText,
        extractTextBeforeCaret,
        getCurrentSelectionRange,
        isInsideMention,
        processMarkdownFormatting
    } from './utils';

    const dispatch = createEventDispatcher<{ 'create-message': string }>();

    let contentEditableDiv: HTMLDivElement;
    let showPopup = false;
    let mentionQuery = '';
    let searchType: 'user' | 'other' | null = null;
    let selectionRange: Range | null = null;
    let rawText = '';
    let isFocusedIn = false;

    const handleInput = () => {
        const range = getCurrentSelectionRange();

        if (!range || isInsideMention(range)) {
            resetMentionState();
            // Still process markdown even if inside mention area
            processMarkdownFormatting(contentEditableDiv);
            rawText = extractRawText(contentEditableDiv);
            return;
        }

        selectionRange = range;
        updateMentionState(range);

        // Process markdown formatting immediately
        processMarkdownFormatting(contentEditableDiv);
        rawText = extractRawText(contentEditableDiv);
    }

    const handleMentionSelect = (event: CustomEvent<LabeledRecord>) => {
        if (!selectionRange) {
            return;
        }

        const mention = event.detail;
        const selection = window.getSelection();

        if (!selection) {
            return;
        }

        insertMention(selection, selectionRange, mention, searchType);
        resetMentionState();
    }

    // Enter = submit, but Enter + Shift = new line
    const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Enter') {
            if (!event.shiftKey) {
                event.preventDefault();
                handleSubmit();
            }
            return;
        }

        // Prevent browser formatting shortcuts that might interfere
        if (event.ctrlKey || event.metaKey) {
            switch (event.key.toLowerCase()) {
                case 'b':
                case 'i':
                case 'u':
                    // Allow these but process markdown after
                    setTimeout(() => {
                        processMarkdownFormatting(contentEditableDiv);
                        rawText = extractRawText(contentEditableDiv);
                    }, 0);
                    break;
            }
        }
    }

    const handleFocus = () => {
        isFocusedIn = true;
        resetMentionState();
    }

    const handleBlur = () => {
        isFocusedIn = false;
    }

    const handlePaste = (event: ClipboardEvent) => {
        event.preventDefault();

        // Get plain text from clipboard
        const text = event.clipboardData?.getData('text/plain') || '';

        // Insert the text at current cursor position
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();
            range.insertNode(document.createTextNode(text));
            range.collapse(false);

            // Process markdown after paste
            setTimeout(() => {
                processMarkdownFormatting(contentEditableDiv);
                rawText = extractRawText(contentEditableDiv);
            }, 0);
        }
    }

    const handleSubmit = () => {
        if (!rawText) {
            return;
        }

        dispatch('create-message', rawText);
    }

    function insertMention(sel: Selection, range: Range, mentionedRecord: LabeledRecord, searchType: 'user' | 'other') {
        // Calculate the start position for deletion, ensuring it's not negative
        const deleteLength = mentionQuery.length + 1; // +1 for the @ or # symbol
        const startOffset = Math.max(0, range.endOffset - deleteLength);

        // Delete the @mentionQuery
        range.setStart(range.endContainer, startOffset);
        range.deleteContents();

        // Insert the mention element
        const mentionSpan = createMentionSpan(mentionedRecord, searchType);
        range.insertNode(mentionSpan);
        const space = document.createTextNode(' ');
        mentionSpan.after(space);

        // Move caret after the inserted space
        const newRange = document.createRange();
        newRange.setStartAfter(space);
        newRange.setEndAfter(space);

        // Update the selection
        sel.removeAllRanges();
        sel.addRange(newRange);

        // Update raw text and process markdown
        rawText = extractRawText(contentEditableDiv);
        processMarkdownFormatting(contentEditableDiv);
    }

    function updateMentionState(range: Range) {
        const textBeforeCaret = extractTextBeforeCaret(contentEditableDiv, range);

        const otherRecordsMatch = buildMentionRegex(MENTION_OTHER_PREFIX).exec(textBeforeCaret);
        const userRecordsMatch = buildMentionRegex(MENTION_USER_PREFIX).exec(textBeforeCaret);

        if (userRecordsMatch) {
            mentionQuery = userRecordsMatch[1];
            showPopup = true;
            searchType = 'user';
            return;
        }

        if (otherRecordsMatch) {
            mentionQuery = otherRecordsMatch[1];
            showPopup = true;
            searchType = 'other';
            return;
        }

        mentionQuery = '';
        showPopup = false;
    }

    function resetMentionState() {
        mentionQuery = '';
        searchType = null;
        selectionRange = null;
        showPopup = false;
    }
</script>

<form class="message-composer-bar" on:submit|preventDefault={handleSubmit}>
    <div class="composer"
         role="textbox"
         tabindex="0"
         contenteditable="true"
         bind:this={contentEditableDiv}
         on:input={handleInput}
         on:keyup={handleInput}
         on:mouseup={handleInput}
         on:focus={handleFocus}
         on:blur={handleBlur}
         on:keydown={handleKeyDown}
         on:paste={handlePaste}>
    </div>

    {#if !rawText && !isFocusedIn}
        <span class="placeholder">Vaše zpráva...</span>
    {/if}

    <button type="submit" disabled="{!rawText}">Send</button>

    {#if showPopup}
        <div class="popover-container">
            <DummyPopover query={mentionQuery} {searchType} on:mention-select={handleMentionSelect}/>
        </div>
    {/if}
</form>

<style lang="less">
    @themed-border-default: #CCC;
    @themed-body-bg-blue-highlighted: #F0F7FF;

    @border-radius-default: 4px;
    @spacing-s: 6px;

    .message-composer-bar {
        display: flex;
        align-items: center;
        gap: @spacing-s;
        position: relative;

        .composer {
            flex: 1;
            border: 1px solid @themed-border-default;
            padding: 8px 12px;
            white-space: pre-wrap;
            border-radius: @border-radius-default;

            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
                outline-offset: 0 !important;
            }

            // Prevent browser from adding unwanted formatting
            font-family: inherit;
            font-size: inherit;
            font-weight: inherit;
            color: inherit;

            :global {
                .mention {
                    position: relative;
                    color: red;
                    white-space: nowrap;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: rgba(255, 0, 0, 0.1);
                        border-radius: 2px;
                    }

                    &.mention-user {
                        color: blue;

                        &::before {
                            background-color: rgba(0, 0, 255, 0.1);
                        }
                    }
                }

                .markdown {
                    &.markdown-bold {
                        font-weight: bold;
                    }

                    &.markdown-italic {
                        font-style: italic;
                    }

                    &.markdown-bold-italic {
                        font-weight: bold;
                        font-style: italic;
                    }

                    &.markdown-strikethrough {
                        text-decoration: line-through;
                    }

                    &.markdown-underline {
                        text-decoration: underline;
                    }

                    &.markdown-code {
                        font-family: 'Courier New', Courier, monospace;
                        background-color: rgba(0, 0, 0, 0.1);
                        padding: 2px 4px;
                        border-radius: 3px;
                        font-size: 0.9em;
                    }
                }

                .markdown-symbol {
                    color: #666;
                    font-weight: normal;
                    font-style: normal;
                    opacity: 0.6;
                }
            }
        }

        .placeholder {
            top: 50%;
            transform: translateY(-50%);
            position: absolute;
            left: 12px;
            pointer-events: none;
            color: #999;
        }

        .popover-container {
            position: absolute;
            bottom: calc(100% + @spacing-s);
            left: 0;
            right: 0;
            width: 100%;
        }
    }
</style>
