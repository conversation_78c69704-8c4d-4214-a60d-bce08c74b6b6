<script lang="ts">
    import type {LabeledRecord} from '../types';
    import {dummyRecords} from './dummy-data';
    import {createEventDispatcher} from 'svelte';

    export let query: string;
    export let searchType: 'user' | 'other';

    const dispatch = createEventDispatcher<{ 'mention-select': LabeledRecord }>();

    $: filteredResults = dummyRecords.filter((record) => record.name.toLowerCase().startsWith(query.toLowerCase())).filter((record) => searchType === 'user' ? record.fond === 7 : record.fond !== 7);
</script>

<div class="dummy-popover">
    {#each filteredResults as record (record.id)}
        <button class="dummy-popover-item" on:click={() => dispatch('mention-select', record)}>
            {record.name}
        </button>
    {/each}
</div>

<style lang="less">
    .dummy-popover {
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        border: 1px solid #CCC;
        padding: 6px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-height: 200px;
        overflow-y: auto;
        background-color: #F5F5F5;
    }
</style>