import type {LabeledRecord} from "./types";
import {
    MARKDOWN_CLASS,
    MARKDOWN_PATTERNS,
    MENTION_CLASS,
    MENTION_ID_ATTRIBUTE,
    MENTION_OTHER_PREFIX,
    MENTION_USER_CLASS,
    MENTION_USER_PREFIX
} from "./constants";

export function createMentionSpan(mentionedRecord: LabeledRecord, searchType: 'user' | 'other'): HTMLSpanElement {
    const mentionSpan = document.createElement('span');
    const prefix = searchType === 'user' ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;
    mentionSpan.textContent = `${prefix}${mentionedRecord.name}`;
    mentionSpan.contentEditable = 'false';
    mentionSpan.className = MENTION_CLASS;

    if (searchType === 'user') {
        mentionSpan.classList.add(MENTION_USER_CLASS);
    }

    mentionSpan.setAttribute(MENTION_ID_ATTRIBUTE, mentionedRecord.id);

    return mentionSpan;
}

export function getCurrentSelectionRange(): Range | null {
    const selection = window.getSelection();
    return selection && selection.rangeCount > 0 ? selection.getRangeAt(0).cloneRange() : null;
}

export function extractTextBeforeCaret(contentEditableDiv: HTMLDivElement, range: Range): string {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(contentEditableDiv);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString();
}

export function isInsideMention(range: Range): boolean {
    const node = range.startContainer;
    const parent = node instanceof Element ? node : node.parentElement;
    return parent?.closest(`.${MENTION_CLASS}`) !== null;
}

export function buildMentionRegex(prefix: string): RegExp {
    return new RegExp(`(?:^|\\s)${prefix}([a-zA-Z0-9_]*)$`);
}

export function extractRawText(container: HTMLElement): string {
    let result = '';

    // Custom iterator to handle mentions and markdown elements
    const iterator = document.createNodeIterator(
        container,
        NodeFilter.SHOW_ALL,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions (they're handled separately)
                if (
                    node.nodeType === Node.TEXT_NODE &&
                    node.parentElement?.classList.contains(MENTION_CLASS)
                ) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    // Traverse the DOM tree and extract text
    let currentNode: Node | null;
    while ((currentNode = iterator.nextNode())) {
        if (currentNode.nodeType === Node.ELEMENT_NODE) {
            const el = currentNode as HTMLElement;
            if (el.classList.contains(MENTION_CLASS)) {
                const id = el.getAttribute(MENTION_ID_ATTRIBUTE);
                result += id ? `@${id}` : '';
            }
            // For markdown elements, we want to include their text content
            // (which includes the formatting symbols)
        } else if (currentNode.nodeType === Node.TEXT_NODE) {
            result += currentNode.textContent ?? '';
        }
    }

    // Remove trailing/leading whitespace
    return result.trim();
}

export function processMarkdownFormatting(container: HTMLElement): void {
    // Get current selection info before processing
    const selection = window.getSelection();
    const selectionInfo = saveSelectionInfo(container, selection);

    // Clear existing markdown formatting first
    clearMarkdownFormatting(container);

    // Process text nodes for markdown formatting
    processTextNodesForMarkdown(container);

    // Restore selection
    restoreSelectionInfo(container, selection, selectionInfo);
}

interface SelectionInfo {
    startOffset: number;
    endOffset: number;
    isCollapsed: boolean;
}

function saveSelectionInfo(container: HTMLElement, selection: Selection | null): SelectionInfo | null {
    if (!selection || selection.rangeCount === 0) {
        return null;
    }

    const range = selection.getRangeAt(0);

    // Use a more robust method to calculate text offsets
    const startOffset = calculateAbsoluteTextOffset(container, range.startContainer, range.startOffset);
    const endOffset = calculateAbsoluteTextOffset(container, range.endContainer, range.endOffset);

    return {
        startOffset,
        endOffset,
        isCollapsed: range.collapsed
    };
}

function restoreSelectionInfo(container: HTMLElement, selection: Selection | null, selectionInfo: SelectionInfo | null): void {
    if (!selection || !selectionInfo) {
        return;
    }

    try {
        const startPos = restoreAbsoluteTextOffset(container, selectionInfo.startOffset);
        const endPos = restoreAbsoluteTextOffset(container, selectionInfo.endOffset);

        if (startPos && endPos) {
            const range = document.createRange();
            range.setStart(startPos.node, startPos.offset);
            range.setEnd(endPos.node, endPos.offset);

            selection.removeAllRanges();
            selection.addRange(range);
        }
    } catch (e) {
        // If restoration fails, place cursor at end
        const range = document.createRange();
        range.selectNodeContents(container);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
    }
}

function processTextNodesForMarkdown(container: HTMLElement): void {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions or existing markdown elements
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS) ||
                    parent?.classList.contains(MARKDOWN_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    const textNodes: Text[] = [];
    let node: Node | null;
    while ((node = walker.nextNode())) {
        textNodes.push(node as Text);
    }

    // Process each text node
    textNodes.forEach(textNode => {
        const text = textNode.textContent || '';
        const formattedHTML = applyMarkdownFormatting(text);

        if (formattedHTML !== text) {
            // Create a temporary container to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedHTML;

            // Replace the text node with the formatted content
            const fragment = document.createDocumentFragment();
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }

            textNode.parentNode?.replaceChild(fragment, textNode);
        }
    });
}

function clearMarkdownFormatting(container: HTMLElement): void {
    const markdownElements = container.querySelectorAll(`.${MARKDOWN_CLASS}`);
    markdownElements.forEach(element => {
        const textContent = element.textContent || '';
        const textNode = document.createTextNode(textContent);
        element.parentNode?.replaceChild(textNode, element);
    });

    // Also clean up unwanted browser formatting (font, b, i, etc.)
    cleanUpBrowserFormatting(container);

    // Normalize text nodes to merge adjacent ones
    container.normalize();
}

function cleanUpBrowserFormatting(container: HTMLElement): void {
    // List of unwanted tags that browsers might add
    const unwantedTags = ['font', 'b', 'i', 'u', 'strike', 'span'];

    unwantedTags.forEach(tagName => {
        const elements = container.querySelectorAll(tagName);
        elements.forEach(element => {
            // Skip our own markdown elements and mentions
            if (element.classList.contains(MARKDOWN_CLASS) ||
                element.classList.contains(MENTION_CLASS) ||
                element.classList.contains('markdown-symbol')) {
                return;
            }

            // Replace the element with its text content
            const textContent = element.textContent || '';
            const textNode = document.createTextNode(textContent);
            element.parentNode?.replaceChild(textNode, element);
        });
    });
}

function getTextOffsetFromNode(container: HTMLElement, targetNode: Node, offset: number): number {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions but include markdown elements
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let totalOffset = 0;
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        if (currentNode === targetNode) {
            return totalOffset + offset;
        }
        totalOffset += currentNode.textContent?.length || 0;
    }

    // If we couldn't find the target node, it might be because we're at the end
    // of a markdown element. Try to find the closest position.
    if (targetNode.nodeType === Node.ELEMENT_NODE) {
        // If target is an element (like after a markdown span),
        // calculate position based on its text content
        const elementText = targetNode.textContent || '';
        return totalOffset + Math.min(offset, elementText.length);
    }

    return totalOffset;
}

function calculateAbsoluteTextOffset(container: HTMLElement, targetNode: Node, offset: number): number {
    // Create a range from the start of container to the target position
    const range = document.createRange();
    range.setStart(container, 0);

    try {
        range.setEnd(targetNode, offset);
        // Get the text content of this range, which gives us the absolute offset
        const textContent = range.toString();
        return textContent.length;
    } catch (e) {
        // Fallback to the old method if range setting fails
        return getTextOffsetFromNode(container, targetNode, offset);
    }
}

function restoreAbsoluteTextOffset(container: HTMLElement, targetOffset: number): {
    node: Node;
    offset: number
} | null {
    // Use a range-based approach to find the position
    const range = document.createRange();
    range.setStart(container, 0);

    // Walk through all nodes to find the position
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_ALL,
        {
            acceptNode(node) {
                // Accept all nodes except mentions
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node as Element;
                    if (element.classList.contains(MENTION_CLASS)) {
                        return NodeFilter.FILTER_REJECT;
                    }
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let currentOffset = 0;
    let node: Node | null;

    while ((node = walker.nextNode())) {
        if (node.nodeType === Node.TEXT_NODE) {
            const textLength = node.textContent?.length || 0;

            if (currentOffset + textLength >= targetOffset) {
                // Found the target text node
                const offsetInNode = targetOffset - currentOffset;
                return {
                    node: node,
                    offset: Math.min(offsetInNode, textLength)
                };
            }

            currentOffset += textLength;
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if we're at the exact boundary of this element
            if (currentOffset === targetOffset) {
                // Position is right before this element
                const prevTextNode = getPreviousTextNode(node);
                if (prevTextNode) {
                    return {
                        node: prevTextNode,
                        offset: prevTextNode.textContent?.length || 0
                    };
                }
            }

            const element = node as Element;
            const elementTextLength = element.textContent?.length || 0;

            if (currentOffset + elementTextLength === targetOffset) {
                // Position is right after this element
                const nextTextNode = getNextTextNode(node);
                if (nextTextNode) {
                    return {
                        node: nextTextNode,
                        offset: 0
                    };
                }

                // If no next text node, position at the end of the last text node in this element
                const lastTextNode = getLastTextNodeInElement(element);
                if (lastTextNode) {
                    return {
                        node: lastTextNode,
                        offset: lastTextNode.textContent?.length || 0
                    };
                }
            }
        }
    }

    // Fallback: find the last text node
    const lastTextNode = getLastTextNode(container);
    if (lastTextNode) {
        return {
            node: lastTextNode,
            offset: lastTextNode.textContent?.length || 0
        };
    }

    return null;
}

function getPreviousTextNode(node: Node): Node | null {
    let current = node.previousSibling;
    while (current) {
        if (current.nodeType === Node.TEXT_NODE) {
            return current;
        }
        if (current.nodeType === Node.ELEMENT_NODE) {
            const lastText = getLastTextNodeInElement(current as Element);
            if (lastText) return lastText;
        }
        current = current.previousSibling;
    }
    return null;
}

function getNextTextNode(node: Node): Node | null {
    let current = node.nextSibling;
    while (current) {
        if (current.nodeType === Node.TEXT_NODE) {
            return current;
        }
        if (current.nodeType === Node.ELEMENT_NODE) {
            const firstText = getFirstTextNodeInElement(current as Element);
            if (firstText) return firstText;
        }
        current = current.nextSibling;
    }
    return null;
}

function getFirstTextNodeInElement(element: Element): Node | null {
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null
    );
    return walker.nextNode();
}

function getLastTextNodeInElement(element: Element): Node | null {
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null
    );

    let lastNode: Node | null = null;
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        lastNode = currentNode;
    }

    return lastNode;
}

function getLastTextNode(container: HTMLElement): Node | null {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let lastNode: Node | null = null;
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        lastNode = currentNode;
    }

    return lastNode;
}

function applyMarkdownFormatting(text: string): string {
    let result = text;

    // Apply markdown patterns in order of priority
    for (const pattern of MARKDOWN_PATTERNS) {
        // Create a new regex instance to avoid global flag issues
        const regex = new RegExp(pattern.regex.source, 'g');
        result = result.replace(regex, (match, delimiters, content) => {
            // Only format if there's actual content between the delimiters
            if (content.trim().length === 0) {
                return match; // Return original text if no content
            }
            return `<${pattern.tag} class="${MARKDOWN_CLASS} ${pattern.class}"><span class="markdown-symbol">${delimiters}</span>${content}<span class="markdown-symbol">${delimiters}</span></${pattern.tag}>`;
        });
    }

    return result;
}