import type {LabeledRecord} from "./types";
import {
    MARKDOWN_CLASS,
    MARKDOWN_PATTERNS,
    MENTION_CLASS,
    MENTION_ID_ATTRIBUTE,
    MENTION_OTHER_PREFIX,
    MENTION_USER_CLASS,
    MENTION_USER_PREFIX
} from "./constants";

export function createMentionSpan(mentionedRecord: LabeledRecord, searchType: 'user' | 'other'): HTMLSpanElement {
    const mentionSpan = document.createElement('span');
    const prefix = searchType === 'user' ? MENTION_USER_PREFIX : MENTION_OTHER_PREFIX;
    mentionSpan.textContent = `${prefix}${mentionedRecord.name}`;
    mentionSpan.contentEditable = 'false';
    mentionSpan.className = MENTION_CLASS;

    if (searchType === 'user') {
        mentionSpan.classList.add(MENTION_USER_CLASS);
    }

    mentionSpan.setAttribute(MENTION_ID_ATTRIBUTE, mentionedRecord.id);

    return mentionSpan;
}

export function getCurrentSelectionRange(): Range | null {
    const selection = window.getSelection();
    return selection && selection.rangeCount > 0 ? selection.getRangeAt(0).cloneRange() : null;
}

export function extractTextBeforeCaret(contentEditableDiv: HTMLDivElement, range: Range): string {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(contentEditableDiv);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString();
}

export function isInsideMention(range: Range): boolean {
    const node = range.startContainer;
    const parent = node instanceof Element ? node : node.parentElement;
    return parent?.closest(`.${MENTION_CLASS}`) !== null;
}

export function buildMentionRegex(prefix: string): RegExp {
    return new RegExp(`(?:^|\\s)${prefix}([a-zA-Z0-9_]*)$`);
}

export function extractRawText(container: HTMLElement): string {
    let result = '';

    // Custom iterator to handle mentions and markdown elements
    const iterator = document.createNodeIterator(
        container,
        NodeFilter.SHOW_ALL,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions (they're handled separately)
                if (
                    node.nodeType === Node.TEXT_NODE &&
                    node.parentElement?.classList.contains(MENTION_CLASS)
                ) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    // Traverse the DOM tree and extract text
    let currentNode: Node | null;
    while ((currentNode = iterator.nextNode())) {
        if (currentNode.nodeType === Node.ELEMENT_NODE) {
            const el = currentNode as HTMLElement;
            if (el.classList.contains(MENTION_CLASS)) {
                const id = el.getAttribute(MENTION_ID_ATTRIBUTE);
                result += id ? `@${id}` : '';
            }
            // For markdown elements, we want to include their text content
            // (which includes the formatting symbols)
        } else if (currentNode.nodeType === Node.TEXT_NODE) {
            result += currentNode.textContent ?? '';
        }
    }

    // Remove trailing/leading whitespace
    return result.trim();
}

export function isInsideMarkdown(range: Range): boolean {
    const node = range.startContainer;
    const parent = node instanceof Element ? node : node.parentElement;
    return parent?.closest(`.${MARKDOWN_CLASS}`) !== null;
}

export function processMarkdownFormatting(container: HTMLElement): void {
    // Save current selection position relative to text content
    const selection = window.getSelection();
    let savedOffset = 0;
    let savedContainer: Node | null = null;

    if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        savedContainer = range.startContainer;
        savedOffset = range.startOffset;

        // If we're in an element, get the text offset from the beginning
        if (savedContainer.nodeType === Node.ELEMENT_NODE) {
            savedOffset = getTextOffsetInContainer(container, savedContainer as Element, savedOffset);
            savedContainer = container;
        } else {
            // Calculate absolute text offset from container start
            savedOffset = getAbsoluteTextOffset(container, savedContainer, savedOffset);
            savedContainer = container;
        }
    }

    // Clear existing markdown formatting first
    clearMarkdownFormatting(container);

    // Process text nodes for markdown formatting
    processTextNodesForMarkdown(container);

    // Restore selection
    if (savedContainer && selection) {
        try {
            const newRange = setSelectionAtTextOffset(container, savedOffset);
            if (newRange) {
                selection.removeAllRanges();
                selection.addRange(newRange);
            }
        } catch (e) {
            // Selection restoration failed, place cursor at end
            const range = document.createRange();
            range.selectNodeContents(container);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        }
    }
}

function processTextNodesForMarkdown(container: HTMLElement): void {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions or existing markdown elements
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS) ||
                    parent?.classList.contains(MARKDOWN_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    const textNodes: Text[] = [];
    let node: Node | null;
    while ((node = walker.nextNode())) {
        textNodes.push(node as Text);
    }

    // Process each text node
    textNodes.forEach(textNode => {
        const text = textNode.textContent || '';
        const formattedHTML = applyMarkdownFormatting(text);

        if (formattedHTML !== text) {
            // Create a temporary container to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = formattedHTML;

            // Replace the text node with the formatted content
            const fragment = document.createDocumentFragment();
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }

            textNode.parentNode?.replaceChild(fragment, textNode);
        }
    });
}

function clearMarkdownFormatting(container: HTMLElement): void {
    const markdownElements = container.querySelectorAll(`.${MARKDOWN_CLASS}`);
    markdownElements.forEach(element => {
        const textContent = element.textContent || '';
        const textNode = document.createTextNode(textContent);
        element.parentNode?.replaceChild(textNode, element);
    });

    // Normalize text nodes to merge adjacent ones
    container.normalize();
}

function getAbsoluteTextOffset(container: HTMLElement, targetNode: Node, offset: number): number {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                // Skip text nodes inside mentions
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let totalOffset = 0;
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        if (currentNode === targetNode) {
            return totalOffset + offset;
        }
        totalOffset += currentNode.textContent?.length || 0;
    }

    return totalOffset;
}

function getTextOffsetInContainer(container: HTMLElement, targetElement: Element, offset: number): number {
    // For element nodes, we need to find the text offset within the container
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let totalOffset = 0;
    let currentNode: Node | null;
    let childIndex = 0;

    while ((currentNode = walker.nextNode())) {
        if (currentNode.parentElement === targetElement && childIndex === offset) {
            return totalOffset;
        }
        if (currentNode.parentElement === targetElement) {
            childIndex++;
        }
        totalOffset += currentNode.textContent?.length || 0;
    }

    return totalOffset;
}

function setSelectionAtTextOffset(container: HTMLElement, targetOffset: number): Range | null {
    const walker = document.createTreeWalker(
        container,
        NodeFilter.SHOW_TEXT,
        {
            acceptNode(node) {
                const parent = node.parentElement;
                if (parent?.classList.contains(MENTION_CLASS)) {
                    return NodeFilter.FILTER_REJECT;
                }
                return NodeFilter.FILTER_ACCEPT;
            }
        }
    );

    let currentOffset = 0;
    let currentNode: Node | null;

    while ((currentNode = walker.nextNode())) {
        const nodeLength = currentNode.textContent?.length || 0;

        if (currentOffset + nodeLength >= targetOffset) {
            const range = document.createRange();
            const offsetInNode = targetOffset - currentOffset;
            range.setStart(currentNode, Math.min(offsetInNode, nodeLength));
            range.setEnd(currentNode, Math.min(offsetInNode, nodeLength));
            return range;
        }

        currentOffset += nodeLength;
    }

    // If we couldn't find the exact position, place at the end
    const range = document.createRange();
    range.selectNodeContents(container);
    range.collapse(false);
    return range;
}

function applyMarkdownFormatting(text: string): string {
    let result = text;

    // Apply markdown patterns in order of priority
    for (const pattern of MARKDOWN_PATTERNS) {
        // Create a new regex instance to avoid global flag issues
        const regex = new RegExp(pattern.regex.source, 'g');
        result = result.replace(regex, (match, delimiters, content) => {
            // Only format if there's actual content between the delimiters
            if (content.trim().length === 0) {
                return match; // Return original text if no content
            }
            return `<${pattern.tag} class="${MARKDOWN_CLASS} ${pattern.class}">${delimiters}${content}${delimiters}</${pattern.tag}>`;
        });
    }

    return result;
}