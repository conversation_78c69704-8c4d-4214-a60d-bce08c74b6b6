export const MENTION_CLASS = 'mention';
export const MENTION_USER_CLASS = 'mention-user';
export const MENTION_ID_ATTRIBUTE = 'data-mention-id';

export const MENTION_USER_PREFIX = '@';
export const MENTION_OTHER_PREFIX = '#';

// Markdown constants
export const MARKDOWN_CLASS = 'markdown';
export const MARKDOWN_BOLD_CLASS = 'markdown-bold';
export const MARKDOWN_ITALIC_CLASS = 'markdown-italic';
export const MARKDOWN_BOLD_ITALIC_CLASS = 'markdown-bold-italic';
export const MARKDOWN_STRIKETHROUGH_CLASS = 'markdown-strikethrough';
export const MARKDOWN_CODE_CLASS = 'markdown-code';
export const MARKDOWN_UNDERLINE_CLASS = 'markdown-underline';

// Markdown patterns (ordered by priority - more specific patterns first)
export const MARKDOWN_PATTERNS = [
    { regex: /(\*\*\*)(.*?)\1/, class: MARKDOWN_BOLD_ITALIC_CLASS, tag: 'strong' },
    { regex: /(___)(.*?)\1/, class: MARKDOWN_BOLD_ITALIC_CLASS, tag: 'strong' },
    { regex: /(\*\*)(.*?)\1/, class: MARKDOWN_BOLD_CLASS, tag: 'strong' },
    { regex: /(__)(.*?)\1/, class: MARKDOWN_UNDERLINE_CLASS, tag: 'u' },
    { regex: /(\*)(.*?)\1/, class: MARKDOWN_ITALIC_CLASS, tag: 'em' },
    { regex: /(_)(.*?)\1/, class: MARKDOWN_ITALIC_CLASS, tag: 'em' },
    { regex: /(~~)(.*?)\1/, class: MARKDOWN_STRIKETHROUGH_CLASS, tag: 'del' },
    { regex: /(`)(.*?)\1/, class: MARKDOWN_CODE_CLASS, tag: 'code' }
];