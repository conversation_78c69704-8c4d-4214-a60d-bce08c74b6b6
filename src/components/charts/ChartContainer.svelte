<script lang="ts">
    import type {ChartOptions} from './charts.types';
    import {createChartContext} from './chart-context';
    import {exists} from '$lib/utils';
    import {onDestroy} from 'svelte';
    import ChartLegend from './legend/ChartLegend.svelte';

    export let options: ChartOptions;

    const context = createChartContext();

    let hoveredLegendLabel: string | null = null;
    const hoveredLabelUnsubscribe = context.hoveredLegendLabel$.subscribe((currentHoveredLabel) => hoveredLegendLabel = currentHoveredLabel);

    onDestroy(() => {
        hoveredLabelUnsubscribe();
    });
</script>

<div class="kp-chart-container type-{options.type} legend-{options.legendOptions?.placement ?? 'none'}" style:--from-chart-offset="{options.legendOptions?.fromChartOffset ?? '0'}">
    {#if exists(options.legendOptions?.placement)}
        <div class="legend-container">
            <ChartLegend {options}/>
        </div>
    {/if}

    <div class="chart-container">
        <slot {hoveredLegendLabel}/>
    </div>
</div>

<style lang="less">
    .kp-chart-container {
        display: flex;
        width: fit-content;
        height: fit-content;
        gap: var(--from-chart-offset);

        &.legend-top {
            flex-direction: column;
        }

        &.legend-bottom {
            flex-direction: column-reverse;
        }

        &.legend-left {
            flex-direction: row;
        }

        &.legend-right {
            flex-direction: row-reverse;
        }

        .chart-container {
            flex: 1 1 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .legend-container {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>