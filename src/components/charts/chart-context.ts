import type {Readable} from 'svelte/store';
import {writable} from 'svelte/store';
import {getContext, setContext} from 'svelte';

const chartContextKey = 'chart-context';

export interface ChartContext {
    hoveredLegendLabel$: Readable<string | null>;
    setHoveredLegendLabelValue: (value: string | null) => void;
}

export function createChartContext(): ChartContext {
    const hoveredLegendLabel$ = writable<string | null>(null);

    return setContext<ChartContext>(chartContextKey, {
        hoveredLegendLabel$,
        setHoveredLegendLabelValue: (value: string | null) => {
            hoveredLegendLabel$.set(value);
        }
    });
}

export function getChartContext(): ChartContext {
    return getContext<ChartContext>(chartContextKey);
}