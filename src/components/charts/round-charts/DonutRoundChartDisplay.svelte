<script lang="ts">
    import type {RoundChartOptions} from './round-charts';
    import {RoundChartUtil} from './round-charts';
    import {exists} from '$lib/utils';
    import {tweened} from 'svelte/motion';
    import {cubicOut} from 'svelte/easing';
    import {onMount} from 'svelte';

    export let options: Extract<RoundChartOptions, { type: 'donut' }>;
    export let hoveredLegendLabel: string | null;

    const hoverStrokeWidth = options.strokeWidth * 1.33;

    const progressTween = tweened(0, {
        duration: 1000,
        easing: cubicOut
    });

    const valueTween = tweened(0, {
        duration: 1000,
        easing: cubicOut
    });

    onMount(() => {
        progressTween.set(1);
        valueTween.set(RoundChartUtil.getSummedUpValues(options));
    });
</script>

<div class="donut-chart" style="width: {options.chartSize}; height: {options.chartSize};">
    <svg width="100%" height="100%" viewBox="0 0 42 42" class="donut">
        <circle class="donut-hole"
                cx="21"
                cy="21"
                r="{21 - hoverStrokeWidth / 2}">
        </circle>

        <circle class="donut-ring"
                cx="21"
                cy="21"
                r="{21 - hoverStrokeWidth / 2}"
                fill="transparent"
                stroke="var(--outline-color)"
                stroke-width="{options.strokeWidth}">
        </circle>

        {#each options.dataset as {color, value, label}, i (label)}
            <circle style="--hoverStrokeWidth: {hoverStrokeWidth}"
                    class="donut-segment"
                    class:is-hovered={exists(hoveredLegendLabel) && hoveredLegendLabel === label}
                    class:not-hovered={exists(hoveredLegendLabel) && hoveredLegendLabel !== label}
                    cx="21"
                    cy="21"
                    r="{21 - hoverStrokeWidth / 2}"
                    fill="transparent"
                    stroke={color}
                    stroke-width="{options.strokeWidth}"
                    stroke-dasharray="{RoundChartUtil.normalizeValue(options, value) * $progressTween} {100 - RoundChartUtil.normalizeValue(options, value) * $progressTween}"
                    stroke-dashoffset="{RoundChartUtil.calculateDonutDashOffset(options, i, $progressTween)}">
            </circle>
        {/each}
    </svg>

    {#if exists($$slots.default)}
        <div class="inner-legend-container">
            <slot data="{RoundChartUtil.sortDataByValue(options)}" showedValue="{Math.round($valueTween)}"/>
        </div>
    {/if}
</div>

<style lang="less">
    @themed-body-bg: #FFF;
    @themed-border-muted: #DDD;

    .donut-chart {
        flex-shrink: 1;
        position: relative;
        display: inline-block;

        .donut-hole {
            fill: @themed-body-bg;
        }

        .donut-ring {
            stroke: @themed-border-muted;
        }

        .donut-segment {
            transition: stroke-width 0.3s ease-in-out, opacity 0.3s ease-in-out;

            &.is-hovered {
                stroke-width: var(--hoverStrokeWidth);
            }

            &.not-hovered {
                opacity: 0.25;
            }
        }

        .inner-legend-container {
            display: flex;
            flex-direction: column;
            position: absolute;
            align-items: center;
            justify-content: center;
            text-align: center;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
</style>