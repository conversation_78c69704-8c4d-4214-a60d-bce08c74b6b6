import type {CssSize} from '$lib/utils';
import type {BaseChartOptions} from '../charts.types';

export type RoundChartType = 'donut';

interface BaseRoundChartOptions extends BaseChartOptions {
    type: RoundChartType;
    startAngle: number;
    chartSize: CssSize;
    dataset: RoundChartDataPoint[];
}

export interface RoundChartDataPoint {
    color: string;
    label: string;
    value: number;
}

type RoundChartOptionsWithStrokeWidth = ({ type: 'donut', strokeWidth: number });

export type RoundChartOptions = BaseRoundChartOptions & RoundChartOptionsWithStrokeWidth;

export class RoundChartUtil {

    public static calculateDonutDashOffset(options: RoundChartOptions, index: number, segmentProgress: number): number {
        let offset = 0;

        for (let i = 0; i < index; i++) {
            offset += (RoundChartUtil.normalizeValue(options, options.dataset[i].value) * segmentProgress);
        }

        return 100 - offset;
    }

    public static sortDataByValue(options: RoundChartOptions): RoundChartDataPoint[] {
        return options.dataset.toSorted((a, b) => b.value - a.value);
    }

    public static getSummedUpValues(options: RoundChartOptions): number {
        return options.dataset.reduce((acc, value) => acc + value.value, 0);
    }

    public static normalizeValue(options: RoundChartOptions, value: number): number {
        return value / RoundChartUtil.getSummedUpValues(options) * 100;
    }
}