<script lang="ts">
    import type {RoundChartOptions, RoundChartType} from './round-charts';
    import DonutRoundChartDisplay from './DonutRoundChartDisplay.svelte';
    import ChartContainer from '../ChartContainer.svelte';

    export let options: RoundChartOptions;

    const typeDisplays: Record<RoundChartType, any> = {
        'donut': DonutRoundChartDisplay
    }
</script>

<ChartContainer {options} let:hoveredLegendLabel>
    <svelte:component this="{typeDisplays[options.type]}" {options} {hoveredLegendLabel}/>
</ChartContainer>