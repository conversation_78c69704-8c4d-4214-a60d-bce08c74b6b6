import type {ChartOptions, LegendItemData} from './charts.types';

export class ChartsUtil {

    public static collectLegendItems(options: ChartOptions): LegendItemData[] {
        switch (options.type) {
            // Bar charts
            case 'columns': {
                if('dataPoints' in options.dataset) {

                }

                return [];
            }
            case 'segmented-columns': {
                if('dataPoints' in options.dataset) {

                }

                return [];
            }
            case 'ratio': {
                return options.dataset.map((barChartRatioDataPoint) => {
                    return {
                        color: barChartRatioDataPoint.color,
                        label: barChartRatioDataPoint.label
                    }
                });
            }
            // Round charts
            case 'donut': {
                return options.dataset.map((roundChartDataPoint) => {
                    return {
                        color: roundChartDataPoint.color,
                        label: roundChartDataPoint.label
                    }
                });
            }
        }
    }
}