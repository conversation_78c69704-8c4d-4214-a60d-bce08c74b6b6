<script lang="ts">
    import type {LegendItemData} from '../charts.types';
    import {getChartContext} from '../chart-context';
    import {onDestroy} from 'svelte';
    import {exists} from '$lib/utils';

    export let legendItem: LegendItemData;

    const context = getChartContext();

    let hoveredLabel: string | null;
    const hoveredLabelUnsubscribe = context.hoveredLegendLabel$.subscribe((currentHoveredLabel) => hoveredLabel = currentHoveredLabel);

    const handleMouseEnterOrLeave = (legendItem: LegendItemData | null) => {
        context.setHoveredLegendLabelValue(legendItem?.label ?? null);
    }

    onDestroy(() => {
        hoveredLabelUnsubscribe();
    });
</script>

<li class="legend-item"
    class:not-hovered={exists(hoveredLabel) && hoveredLabel !== legendItem.label}
    on:mouseenter={() => handleMouseEnterOrLeave(legendItem)}
    on:mouseleave={() => handleMouseEnterOrLeave(null)}>

    <div class="color-box" style:background-color="{legendItem.color}"></div>
    <span class="legend-label">{legendItem.label}</span>
</li>

<style lang="less">
    @spacing-sm: 10px;
    @border-radius-sm: 3px;
    @themed-panel-bg: #F8F8F8;

    .legend-item {
        position: relative;
        display: flex;
        align-items: center;
        gap: @spacing-sm;
        cursor: pointer;
        transition: opacity 0.3s ease-in-out;
        isolation: isolate;

        &:before {
            content: '';
            top: -3px;
            left: -3px;
            bottom: -3px;
            right: -3px;
            z-index: -1;
            border-right: @border-radius-sm;
            background-color: @themed-panel-bg;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
        }

        &:hover {
            &:before {
                opacity: 1;
                visibility: visible;
            }
        }

        &.not-hovered {
            opacity: 0.25;
        }

        .color-box {
            width: 16px;
            height: 16px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: @border-radius-sm;
        }
    }
</style>