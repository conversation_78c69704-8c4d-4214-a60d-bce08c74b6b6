<script lang="ts">
    import type {ChartOptions} from '../charts.types';
    import {ChartsUtil} from '../charts-util';
    import LegendItem from './LegendItem.svelte';

    export let options: ChartOptions;

    $: legendItems = ChartsUtil.collectLegendItems(options);
</script>

<ul class="legend-container legend-orientation-{options.legendOptions?.orientation ?? 'none'}" style:--gap="{options.legendOptions?.gap ?? '10px'}">
    {#each legendItems as legendItem}
        <LegendItem {legendItem}/>
    {/each}
</ul>

<style lang="less">
    @spacing-sm: 10px;

    .legend-container {
        display: flex;
        gap: var(--gap);
        list-style: none;
        margin: 0;
        padding: 0;

        &.legend-orientation-row,
        &.legend-orientation-none {
            flex-direction: row;
            flex-wrap: wrap;
        }

        &.legend-orientation-column {
            flex-direction: column;
        }
    }
</style>