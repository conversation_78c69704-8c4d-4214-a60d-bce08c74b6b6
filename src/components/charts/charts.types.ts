import type {BarChartOptions} from './bar-charts/bar-charts';
import type {RoundChartOptions} from './round-charts/round-charts';
import type {CssSize} from '$lib/utils';

export interface BaseChartOptions {
    legendOptions?: LegendOptions;
}

interface LegendOptions {
    placement?: LegendPlacement;
    orientation?: LegendOrientation;
    fromChartOffset?: CssSize;
    gap?: CssSize;
}

export type LegendPlacement = 'top' | 'bottom' | 'left' | 'right';
export type LegendOrientation = 'column' | 'row';

export type ChartOptions = BarChartOptions | RoundChartOptions;

export interface LegendItemData {
    label: string;
    color: string;
}