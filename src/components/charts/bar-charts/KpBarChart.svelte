<script lang="ts">
    import type {BarChartOptions, BarChartType} from './bar-charts';
    import type {SvelteComponentConstructor} from '$lib/utils';
    import RatioBarChartDisplay from './RatioBarChartDisplay.svelte';
    import ColumnsBarChartDisplay from './ColumnsBarChartDisplay.svelte';
    import SegmentedColumnsBarChartDisplay from './SegmentedColumnsBarChartDisplay.svelte';
    import ChartContainer from '../ChartContainer.svelte';

    export let options: BarChartOptions;

    const typeDisplays: Record<BarChartType, SvelteComponentConstructor> = {
        'ratio': RatioBarChartDisplay,
        'columns': ColumnsBarChartDisplay,
        'segmented-columns': SegmentedColumnsBarChartDisplay,
    }
</script>

<ChartContainer {options} let:hoveredLegendLabel>
    <svelte:component this="{typeDisplays[options.type]}" {options} {hoveredLegendLabel}/>
</ChartContainer>