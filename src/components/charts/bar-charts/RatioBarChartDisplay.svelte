<script lang="ts">
    import {type BarChartOptions, BarChartUtil} from './bar-charts';
    import {tweened} from 'svelte/motion';
    import {cubicOut} from 'svelte/easing';
    import {onMount} from 'svelte';
    import {exists} from '$lib/utils';

    export let options: Extract<BarChartOptions, { type: 'ratio' }>;
    export let hoveredLegendLabel: string | null;

    const totalValue = options.maxValue ?? options.dataset.reduce((acc, value) => acc + value.value, 0);

    const progressTween = tweened(0, {
        duration: 1000,
        easing: cubicOut
    });

    onMount(() => {
        progressTween.set(1);
    });
</script>

<div class="kp-ratio-bar-chart orientation-{options.orientation}" style="{BarChartUtil.getDimension(options)}">
    {#each options.dataset as {value, label, color}}
        <div class="ratio-bar-chart-item"
             class:not-hovered={exists(hoveredLegendLabel) && hoveredLegendLabel !== label}
             style:--width-ratio="{(value / totalValue) * $progressTween}"
             style:background-color="{color}"></div>
    {/each}
</div>

<style lang="less">
    @second-dimension: 64px;

    @border-radius-large: 6px;
    @themed-panel-bg: #F8F8F8;
    @themed-border-muted: #DDD;

    .kp-ratio-bar-chart {
        display: flex;
        border: 1px solid @themed-border-muted;
        border-radius: @border-radius-large;
        background-color: @themed-panel-bg;

        &.orientation-horizontal {
            flex-direction: row;
            width: 100%;
            height: @second-dimension;

            .ratio-bar-chart-item {
                height: @second-dimension;
                width: calc(var(--width-ratio) * 100%);

                &:first-child {
                    border-top-left-radius: @border-radius-large;
                    border-bottom-left-radius: @border-radius-large;
                }

                &:last-child {
                    border-top-right-radius: @border-radius-large;
                    border-bottom-right-radius: @border-radius-large;
                }
            }
        }

        &.orientation-vertical {
            flex-direction: column;
            height: 100%;
            width: @second-dimension;

            .ratio-bar-chart-item {
                width: @second-dimension;
                height: calc(var(--width-ratio) * 100%);

                &:first-child {
                    border-top-left-radius: @border-radius-large;
                    border-top-right-radius: @border-radius-large;
                }

                &:last-child {
                    border-bottom-left-radius: @border-radius-large;
                    border-bottom-right-radius: @border-radius-large;
                }
            }
        }

        .ratio-bar-chart-item {
            transition: opacity 0.3s ease-in-out;

            &.not-hovered {
                opacity: 0.25;
            }
        }
    }
</style>