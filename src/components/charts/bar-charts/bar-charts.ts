import type {BaseChartOptions} from '../charts.types';
import type {CssSize} from '$lib/utils';

export type BarChartType = 'columns' | 'segmented-columns' | 'ratio';
type BarChartOrientation = 'vertical' | 'horizontal';

// Datasets
export interface BarChartColumnsDataPoint {
    color: string;
    label: string;
    value: number;
}

export interface BarChartSegmentedColumnsDataPoint {
    label: string;
    segments: BarChartColumnsDataPoint[];
}

export interface BarChartRatioDataPoint {
    color: string;
    label: string;
    value: number;
}

export interface BarChartMultipleColumnsDataPoint {
    label: string;
    dataPoints: BarChartColumnsDataPoint[];
}

export interface BarChartMultipleSegmentedColumnsDataPoint {
    label: string;
    dataPoints: BarChartSegmentedColumnsDataPoint[];
}

// Options
interface BaseBarChartOptions extends BaseChartOptions {
    type: BarChartType;
    orientation: BarChartOrientation;
}

type BarChartOptionsWithDimensions = (
    | { orientation: 'horizontal'; width: CssSize }
    | { orientation: 'vertical'; height: CssSize });

type BarChartOptionsWithDataset = (
    | { type: 'columns'; dataset: BarChartColumnsDataPoint[] | BarChartMultipleColumnsDataPoint[] }
    | { type: 'segmented-columns'; dataset: BarChartSegmentedColumnsDataPoint[] | BarChartMultipleSegmentedColumnsDataPoint[] }
    | { type: 'ratio'; dataset: BarChartRatioDataPoint[]; maxValue?: number; });

export type BarChartOptions = BaseBarChartOptions & BarChartOptionsWithDimensions & BarChartOptionsWithDataset;

export class BarChartUtil {

    public static getDimension(options: BarChartOptionsWithDimensions): string {
        if ('width' in options) {
            return `width: ${options.width};`;
        }

        if ('height' in options) {
            return `height: ${options.height};`;
        }

        return '';
    }
}