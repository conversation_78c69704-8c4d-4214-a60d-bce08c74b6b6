import type {ComponentType, SvelteComponent} from 'svelte';

export function isNull(value: unknown): boolean {
    return value === null;
}

export function isNullOrUndefined(value: unknown): boolean {
    return isNull(value) || isUndefined(value);
}

export function isUndefined(value: unknown): boolean {
    return typeof value === 'undefined';
}

export function exists(value: unknown): boolean {
    return !isNull(value) && !isUndefined(value);
}

export type Px = `${number}px`;
export type Em = `${number}em`;
export type Rem = `${number}rem`;
export type Percent = `${number}%`;
export type Vw = `${number}vw`;
export type Vh = `${number}vh`;
export type Vmin = `${number}vmin`;
export type Vmax = `${number}vmax`;
export type Ch = `${number}ch`;
export type Ex = `${number}ex`;
export type Pt = `${number}pt`;
export type Cm = `${number}cm`;
export type Mm = `${number}mm`;
export type In = `${number}in`;
export type Pc = `${number}pc`;
export type Fr = `${number}fr`;
export type Auto = 'auto';

export type CssSize = Px | Em | Rem | Percent | Vw | Vh | Vmin | Vmax | Ch | Ex | Pt | Cm | Mm | In | Pc | Fr | Auto | 0;

export type SvelteComponentConstructor<PROPS extends Record<string, any> = Record<string, any>> = ComponentType<SvelteComponent<PROPS>>;

export function range(start: number, stop: number, step: number = 1): number[] {
    if (step > 0 && stop < start) {
        throw new Error(`For step of ${step} start value should be less or equal to stop value`);
    }
    if (step < 0 && stop > start) {
        throw new Error(`For step of ${step} start value should be more or equal to stop value`);
    }
    return Array.from({length: (stop - start) / step + 1}, (_, i) => start + (i * step));
}

export function getColorFromString(str: string, lightness: number, opacity?: number): string {
    if (!str) {
        return `hsl(0, 0%, ${lightness}%)`;
    }

    const hues = range(0, 360, 10);
    const goldenFrac = 0.5 * (3 - Math.sqrt(5));
    const x = (simpleHash(str) * goldenFrac % 1.0) * (hues.length - 1);
    const i = Math.floor(x);
    const f = x % 1.0;
    const hue = (1.0 - f) * hues[i] + f * hues[i + 1];

    if (exists(opacity)) {
        return `hsla(${Math.round(hue * 100) / 100}, 100%, ${lightness}%, ${opacity})`;
    }

    return `hsl(${Math.round(hue * 100) / 100}, 100%, ${lightness}%)`;
}

function simpleHash(str: string): number {
    const prime = 31;
    let hash = 7;

    for (let i = 0; i < str.length; i++) {
        hash = (hash * prime + str.charCodeAt(i)) % Number.MAX_SAFE_INTEGER;
    }

    return hash;
}